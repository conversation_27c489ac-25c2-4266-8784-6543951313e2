import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, loadTemplateFile, replacePlaceholders, generatePDFStyles } from '@/lib/templates';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, data } = body;
    
    if (!templateId || !data) {
      return NextResponse.json(
        { error: 'Template ID and data are required' },
        { status: 400 }
      );
    }

    // Load templates to find the one to generate
    const templates = await loadTemplates();
    const template = templates.find(t => t.id === templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Load the template HTML content
    const templateContent = await loadTemplateFile(template.filename);

    // Replace placeholders with actual data
    const processedContent = replacePlaceholders(templateContent, data);

    // Add PDF-specific styles based on layout size
    const pdfStyles = generatePDFStyles(template.layoutSize);
    const htmlWithStyles = processedContent.replace('</head>', `${pdfStyles}</head>`);

    // Return the HTML content with PDF styles for client-side PDF generation
    return NextResponse.json({
      success: true,
      htmlContent: htmlWithStyles,
      templateName: template.name,
      layoutSize: template.layoutSize
    });
  } catch (error) {
    console.error('Error generating document:', error);
    return NextResponse.json(
      { error: 'Failed to generate document' },
      { status: 500 }
    );
  }
}
