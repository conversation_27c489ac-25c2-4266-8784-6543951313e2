import { NextRequest, NextResponse } from 'next/server';
import { loadTemplates, reformatTemplateFile, extractPlaceholders, updateTemplate, loadTemplateFile } from '@/lib/templates';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId } = body;
    
    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    // Load templates to find the one to reformat
    const templates = await loadTemplates();
    const template = templates.find(t => t.id === templateId);
    
    if (!template) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Reformat the template file
    await reformatTemplateFile(template.filename);
    
    // Re-extract placeholders from the cleaned content
    const cleanedContent = await loadTemplateFile(template.filename);
    const newPlaceholders = extractPlaceholders(cleanedContent);
    
    // Update the template metadata with new placeholders
    await updateTemplate(templateId, {
      placeholders: newPlaceholders
    });

    return NextResponse.json({
      success: true,
      message: 'Template reformatted successfully',
      placeholders: newPlaceholders
    });
  } catch (error) {
    console.error('Error reformatting template:', error);
    return NextResponse.json(
      { error: 'Failed to reformat template' },
      { status: 500 }
    );
  }
}
