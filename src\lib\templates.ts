import { promises as fs } from 'fs';
import path from 'path';

export interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: 'A4' | 'Letter';
  uploadedAt: string;
}

const TEMPLATES_DIR = path.join(process.cwd(), 'public', 'templates');
const TEMPLATES_JSON_PATH = path.join(TEMPLATES_DIR, 'templates.json');

// Ensure templates directory exists
export async function ensureTemplatesDir() {
  try {
    await fs.access(TEMPLATES_DIR);
  } catch {
    await fs.mkdir(TEMPLATES_DIR, { recursive: true });
  }
}

// Load all templates from templates.json
export async function loadTemplates(): Promise<Template[]> {
  try {
    await ensureTemplatesDir();
    const data = await fs.readFile(TEMPLATES_JSON_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading templates:', error);
    return [];
  }
}

// Save templates to templates.json
export async function saveTemplates(templates: Template[]): Promise<void> {
  await ensureTemplatesDir();
  await fs.writeFile(TEMPLATES_JSON_PATH, JSON.stringify(templates, null, 2));
}

// Add a new template
export async function addTemplate(template: Omit<Template, 'uploadedAt'>): Promise<void> {
  const templates = await loadTemplates();
  const newTemplate: Template = {
    ...template,
    uploadedAt: new Date().toISOString(),
  };
  templates.push(newTemplate);
  await saveTemplates(templates);
}

// Update an existing template
export async function updateTemplate(id: string, updates: Partial<Template>): Promise<void> {
  const templates = await loadTemplates();
  const index = templates.findIndex(t => t.id === id);
  if (index !== -1) {
    templates[index] = { ...templates[index], ...updates };
    await saveTemplates(templates);
  }
}

// Delete a template
export async function deleteTemplate(id: string): Promise<void> {
  const templates = await loadTemplates();
  const filteredTemplates = templates.filter(t => t.id !== id);
  await saveTemplates(filteredTemplates);
  
  // Also delete the HTML file
  const template = templates.find(t => t.id === id);
  if (template) {
    try {
      await fs.unlink(path.join(TEMPLATES_DIR, template.filename));
    } catch (error) {
      console.error('Error deleting template file:', error);
    }
  }
}

// Extract placeholders from HTML content
export function extractPlaceholders(htmlContent: string): string[] {
  const placeholderRegex = /\[([^\]]+)\]/g;
  const placeholders = new Set<string>();
  let match;

  while ((match = placeholderRegex.exec(htmlContent)) !== null) {
    placeholders.add(match[0]); // Include the brackets
  }

  return Array.from(placeholders).sort();
}

// Clean HTML content from Word-specific tags and attributes (minimal cleaning to preserve formatting)
export function cleanWordHtml(htmlContent: string): string {
  let cleaned = htmlContent;

  // Remove only the most problematic Word-specific XML namespaces and tags
  cleaned = cleaned.replace(/<\/?o:[^>]*>/g, '');
  cleaned = cleaned.replace(/<\/?w:[^>]*>/g, '');
  cleaned = cleaned.replace(/<\/?v:[^>]*>/g, '');

  // Remove Word-specific meta tags and generator info
  cleaned = cleaned.replace(/<meta[^>]*name="Generator"[^>]*>/gi, '');
  cleaned = cleaned.replace(/<meta[^>]*name="ProgId"[^>]*>/gi, '');
  cleaned = cleaned.replace(/<meta[^>]*content="Microsoft Word[^>]*>/gi, '');

  // Remove problematic font definitions but keep basic styles
  cleaned = cleaned.replace(/<style>[\s\S]*?\/\* Font Definitions \*\/[\s\S]*?<\/style>/gi, '');
  cleaned = cleaned.replace(/<style>[\s\S]*?@font-face[\s\S]*?<\/style>/gi, '');

  // Keep WordSection1 class as it might be important
  // cleaned = cleaned.replace(/<div class=WordSection1>/gi, '<div>');

  // Remove only the most problematic classes, keep MsoNormal and MsoNoSpacing
  cleaned = cleaned.replace(/class="[^"]*MsoChpDefault[^"]*"/gi, '');
  cleaned = cleaned.replace(/class="[^"]*MsoPapDefault[^"]*"/gi, '');

  // Remove language attributes
  cleaned = cleaned.replace(/lang=EN-US/gi, '');

  // Preserve ALL spacing, alignment, margin, and text-indent styles as they're crucial for Word formatting

  // Remove only truly empty attributes
  cleaned = cleaned.replace(/\s*class=""\s*/g, ' ');
  cleaned = cleaned.replace(/\s*style=""\s*/g, ' ');
  cleaned = cleaned.replace(/\s*style=''\s*/g, ' ');

  // Remove problematic characters but keep &nbsp; as it's important for spacing
  cleaned = cleaned.replace(/�+/g, ' ');

  // Don't remove empty spans and divs as they might be important for Word formatting

  // Minimal formatting cleanup
  cleaned = cleaned.replace(/><p/g, '>\n<p');
  cleaned = cleaned.replace(/<\/p></g, '</p>\n<');
  cleaned = cleaned.replace(/><head/g, '>\n<head');
  cleaned = cleaned.replace(/<\/head></g, '</head>\n<');
  cleaned = cleaned.replace(/><body/g, '>\n<body');
  cleaned = cleaned.replace(/<\/body></g, '</body>\n<');

  // Minimal whitespace cleanup - preserve most spacing
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');

  // Add proper DOCTYPE and structure if missing
  if (!cleaned.includes('<!DOCTYPE')) {
    cleaned = '<!DOCTYPE html>\n' + cleaned;
  }

  // Add basic CSS for better formatting
  const basicStyles = `
<style>
  body {
    font-family: 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.5;
    margin: 1in;
    color: black;
  }
  p {
    margin: 0 0 10pt 0;
  }
  .center {
    text-align: center;
  }
  .justify {
    text-align: justify;
  }
  .bold {
    font-weight: bold;
  }
  .underline {
    text-decoration: underline;
  }
</style>`;

  // Insert basic styles after head tag
  if (cleaned.includes('<head>')) {
    cleaned = cleaned.replace('<head>', '<head>\n' + basicStyles);
  }

  // Replace only images with Tanauan logo alt text with the official logo
  cleaned = cleaned.replace(
    /<img([^>]*?)alt="[^"]*tanauan[^"]*logo[^"]*"([^>]*?)src="[^"]*"([^>]*?)>/gi,
    '<img$1alt="Tanauan Logo"$2src="/images/Tanauan_logo.jpg"$3>'
  );

  // Also handle the reverse order (src before alt)
  cleaned = cleaned.replace(
    /<img([^>]*?)src="[^"]*"([^>]*?)alt="[^"]*tanauan[^"]*logo[^"]*"([^>]*?)>/gi,
    '<img$1src="/images/Tanauan_logo.jpg"$2alt="Tanauan Logo"$3>'
  );

  return cleaned.trim();
}

// Generate a unique filename for a template
export function generateTemplateFilename(name: string): string {
  const slug = name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');

  return `${slug}.html`;
}

// Save HTML content to a template file
export async function saveTemplateFile(filename: string, htmlContent: string): Promise<void> {
  await ensureTemplatesDir();
  const filePath = path.join(TEMPLATES_DIR, filename);
  await fs.writeFile(filePath, htmlContent, 'utf-8');
}

// Load HTML content from a template file
export async function loadTemplateFile(filename: string): Promise<string> {
  const filePath = path.join(TEMPLATES_DIR, filename);
  return await fs.readFile(filePath, 'utf-8');
}

// Reformat and clean an existing template file
export async function reformatTemplateFile(filename: string): Promise<void> {
  const filePath = path.join(TEMPLATES_DIR, filename);
  const originalContent = await fs.readFile(filePath, 'utf-8');
  const cleanedContent = cleanWordHtml(originalContent);
  await fs.writeFile(filePath, cleanedContent, 'utf-8');
}

// Replace placeholders in HTML content with actual values
export function replacePlaceholders(htmlContent: string, data: Record<string, string>): string {
  let processedContent = htmlContent;

  // Replace each placeholder with its corresponding value
  Object.entries(data).forEach(([key, value]) => {
    const placeholder = `[${key}]`;
    const regex = new RegExp(placeholder.replace(/[[\]]/g, '\\$&'), 'g');
    processedContent = processedContent.replace(regex, value || '');
  });

  return processedContent;
}

// Generate PDF from HTML content with specified layout size
export function generatePDFStyles(layoutSize: 'A4' | 'Letter'): string {
  const pageStyles = layoutSize === 'A4'
    ? {
        width: '210mm',
        height: '297mm',
        margin: '1in'
      }
    : {
        width: '8.5in',
        height: '11in',
        margin: '1in'
      };

  return `
    <style>
      @page {
        size: ${layoutSize};
        margin: ${pageStyles.margin};
      }

      @media print {
        body {
          width: ${pageStyles.width};
          height: ${pageStyles.height};
          margin: 0;
          padding: ${pageStyles.margin};
          font-family: 'Times New Roman', serif;
          font-size: 12pt;
          line-height: 1.5;
          color: black;
          background: white;
        }

        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
      }

      body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.15;
        color: black;
        background: white;
        margin: 0;
        padding: 0;
        width: 100%;
        box-sizing: border-box;
        word-wrap: break-word;
      }

      /* Word-specific paragraph styles */
      p.MsoNoSpacing, p.MsoNormal {
        margin: 0;
        margin-bottom: 0.0001pt;
        font-size: 12.0pt;
        font-family: 'Times New Roman', serif;
        color: black;
      }

      p {
        margin: 0 0 6pt 0;
        font-size: 12pt;
        font-family: 'Times New Roman', serif;
        color: black;
      }

      /* Preserve Word text alignment */
      [align="center"], .center, [style*="text-align:center"] {
        text-align: center !important;
      }

      [style*="text-align:justify"], .justify {
        text-align: justify !important;
        text-justify: inter-ideograph;
      }

      /* Preserve Word indentation */
      [style*="text-indent"] {
        /* Preserve original text-indent values */
      }

      [style*="margin-left"] {
        /* Preserve original margin-left values */
      }

      /* Preserve Word spacing */
      [style*="margin-right"] {
        /* Preserve original margin-right values */
      }

      /* Bold and underline */
      b, strong, .bold {
        font-weight: bold;
      }

      u, .underline {
        text-decoration: underline;
      }

      /* Preserve Word line height */
      [style*="line-height:115%"] {
        line-height: 1.15 !important;
      }

      /* Word section container */
      .WordSection1 {
        min-height: calc(100vh - 2in);
        overflow: visible;
        width: 100%;
      }

      div {
        width: 100%;
      }

      /* Preserve all inline styles */
      [style] {
        /* Let inline styles take precedence */
      }
    </style>
  `;
}
